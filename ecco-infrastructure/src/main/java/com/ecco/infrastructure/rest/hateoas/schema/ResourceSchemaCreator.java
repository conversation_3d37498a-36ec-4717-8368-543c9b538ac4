package com.ecco.infrastructure.rest.hateoas.schema;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.DefaultSerializerProvider;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.factories.JsonSchemaFactory;
import com.fasterxml.jackson.module.jsonSchema.factories.SchemaFactoryWrapper;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;
import com.fasterxml.jackson.module.jsonSchema.types.ObjectSchema;
import com.fasterxml.jackson.module.jsonSchema.types.SimpleTypeSchema;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.hateoas.server.core.DummyInvocationUtils;
import org.springframework.hateoas.server.core.LastInvocationAware;
import org.springframework.hateoas.server.core.LinkBuilderSupport;
import org.springframework.hateoas.server.core.MethodInvocation;
import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ValueConstants;

import org.jspecify.annotations.Nullable;
import java.lang.annotation.Annotation;
import java.lang.reflect.Parameter;
import java.lang.reflect.Proxy;
import java.net.URI;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * Creates a (draft-3) JSON Schema (http://tools.ietf.org/html/draft-zyp-json-schema-03")
 * for a given resource class.
 */
@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class ResourceSchemaCreator implements ApplicationContextAware {
    private static final String HTTP_JSON_SCHEMA_ORG_DRAFT_03_SCHEMA = "http://json-schema.org/draft-03/schema#";
    private final SecurityChecker securityChecker;
    private final ObjectMapper objectMapper;
    private final JsonSchemaFactory schemaFactory;
    private final EnumSchemaCreator enumSchemaCreator;
    private ApplicationContext applicationContext;

    public ResourceSchemaCreator(SecurityChecker securityChecker, ObjectMapper objectMapper, EnumSchemaCreator enumSchemaCreator, JsonSchemaFactory schemaFactory) {
        this.securityChecker = securityChecker;
        this.objectMapper = objectMapper;
        this.schemaFactory = schemaFactory;
        this.enumSchemaCreator = enumSchemaCreator;
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * Creates a (serializable) JSON schema object describing a resource. The resource can be annotated with {@code javax.validation}
     * constraints as well as {@link JsonSchemaProperty @JsonSchemaProperty} and {@link JsonSchemaMetadata @JsonSchemaMetadata}
     * to enrich the content of this schema object, otherwise it will simply be fields and types.
     * <p>
     * The {@code Object} method references passed here are {@link org.springframework.hateoas.server.core.LastInvocationAware}
     * objects obtained from Spring HATEOAS with {@link WebMvcLinkBuilder#methodOn(Class, Object...)}.
     * The {@code listControllerMethod} * and {@code createControllerMethod} are optional, and will be checked via Spring Security to ensure
     * the current user can access them before links are returned as part of the schema. The {@code schemaMethod} will be
     * used as the ID of the schema itself.
     * <p>
     * The schema returned is in the <a href='http://tools.ietf.org/html/draft-zyp-json-schema-03'>JSON Schema draft-3</a> model.
     *
     * @param resourceClass the resource to describe
     * @param schemaMethod a {@code LastInvocationAware} method reference to the schema controller method
     * @param listControllerMethod  a {@code LastInvocationAware} method reference to the controller method to GET a list of instances of these resources
     * @param createControllerMethod a {@code LastInvocationAware} method reference to the controller method to POST a new isntance of this resource
     * @param <T> the resource type to describe
     * @return a schema object describing the resource, which can be serializaed to JSON itself
     */
    public <T extends RepresentationModel<T>> JsonSchema create(Class<T> resourceClass, Object schemaMethod, Optional<Object> listControllerMethod, Optional<Object> createControllerMethod) {
        JsonSchema jsonSchema = createFullSchema(resourceClass);
        decorateJsonSchema(jsonSchema, schemaMethod, listControllerMethod, createControllerMethod);

        return jsonSchema;
    }

    /**
     * As per {@link #create(Class, Object, Optional, Optional)} except that the schema URI is specified explicitly.
     * @param resourceClass the resource to describe
     * @param schemaUri the URI to return as the schema identity
     * @param listControllerMethod  a {@code LastInvocationAware} method reference to the controller method to GET a list of instances of these resources
     * @param createControllerMethod a {@code LastInvocationAware} method reference to the controller method to POST a new isntance of this resource
     * @param <T> the resource type to describe
     * @return a schema object describing the resource, which can be serializaed to JSON itself
     */
    public <T extends RepresentationModel<T>> JsonSchema create(Class<T> resourceClass, URI schemaUri, Optional<Object> listControllerMethod, Optional<Object> createControllerMethod) {
        JsonSchema jsonSchema = createFullSchema(resourceClass);
        decorateJsonSchema(jsonSchema, schemaUri, listControllerMethod, createControllerMethod);

        return jsonSchema;
    }

    public <T extends RepresentationModel<T>> JsonSchema createEnum(List<T> resources, Object enumMethod, Function<T, String> valueFn, @Nullable Function<T, String> nameFn) {
        final Stream<T> resourceStream = resources.stream();
        return createEnum(resourceStream, enumMethod, valueFn, nameFn, null);
    }

    public <T extends RepresentationModel<T>> JsonSchema createEnum(Stream<T> resourceStream, Object enumMethod, Function<T, String> valueFn,
                                                             @Nullable Function<T, String> nameFn,
                                                             @Nullable Function<T, String> descriptionFn) {
        JsonSchema jsonSchema = enumSchemaCreator.createEnum(resourceStream, valueFn, nameFn, descriptionFn, schemaFactory::stringSchema);
        decorateJsonSchema(jsonSchema, enumMethod, Optional.empty(), Optional.empty());
        return jsonSchema;
    }

    public <T> JsonSchema createNestedEnum(Stream<T> resources, Object enumMethod, Function<T, SimpleTypeSchema> valueFn,
                                           @Nullable Function<T, String> nameFn,
                                           @Nullable Function<T, String> descriptionFn) {
        JsonSchema jsonSchema = enumSchemaCreator.createNestedEnum(resources, valueFn, nameFn, descriptionFn);
        decorateJsonSchema(jsonSchema, enumMethod, Optional.empty(), Optional.empty());
        return jsonSchema;
    }

    /**
     * Creates a (serializable) JSON schema object describing the request parameters to a controller method.
     * The parameters can be annotated with {@code javax.validation}
     * constraints as well as {@link JsonSchemaProperty @JsonSchemaProperty} and {@link JsonSchemaMetadata @JsonSchemaMetadata}
     * to enrich the content of this schema object, otherwise it will simply be parameter names and types.
     * <p>
     * The {@code Object} method reference passed here is a {@link org.springframework.hateoas.server.core.LastInvocationAware}
     * object obtained from Spring HATEOAS with {@link WebMvcLinkBuilder#methodOn(Class, Object...)}.
     * The {@code controllerMethod} will be
     * used as the ID of the schema itself.
     * <p>
     * The schema returned is in the <a href='http://tools.ietf.org/html/draft-zyp-json-schema-03'>JSON Schema draft-3</a> model.
     *
     * @param controllerMethod a {@code LastInvocationAware} method reference to the controller method with all parameters populated.
     * @return a schema object describing the request parameters, which can be serialized to JSON itself.
     */
    public JsonSchema createForRequestParams(Object controllerMethod) {
        // Not really a concept of a visitor for parameters... can we create one for a map?
        Assert.isInstanceOf(LastInvocationAware.class, controllerMethod);

        Parameter[] parameters = DummyInvocationUtils.getLastInvocationAware(controllerMethod).getLastInvocation().getMethod().getParameters();
        SerializationConfig serializationConfig = objectMapper.getSerializationConfig();
        DefaultSerializerProvider.Impl serializerProvider = ((DefaultSerializerProvider.Impl) objectMapper.getSerializerProvider()).createInstance(serializationConfig, objectMapper.getSerializerFactory());
        CustomSchemaFactoryWrapper objectWrapper = new CustomSchemaFactoryWrapper(enumSchemaCreator, objectMapper, applicationContext);
        CustomSchemaFactoryWrapper.ObjectVisitorDecorator propertyEnhancer = (CustomSchemaFactoryWrapper.ObjectVisitorDecorator) objectWrapper.expectObjectFormat(TypeFactory.unknownType());
        ObjectSchema objectSchema = objectWrapper.finalSchema().asObjectSchema();

        try {
            SchemaFactoryWrapper propertyWrapper = new CustomSchemaFactoryWrapper(enumSchemaCreator, objectMapper, applicationContext);
            for (Parameter parameter : parameters) {
                if (parameter.getAnnotation(RequestParam.class) != null) {
                    JavaType javaType = serializationConfig.constructType(parameter.getType());
                    BeanProperty beanProperty = beanPropertyForParameter(parameter, javaType);

                    JsonSerializer<Object> ser = serializerProvider.findTypedValueSerializer(javaType, false, beanProperty);
                    ser.acceptJsonFormatVisitor(propertyWrapper, javaType);
                    PropertyMetadata propertyMetadata = beanProperty.getMetadata();
                    if (propertyMetadata.isRequired()) {
                        objectSchema.putProperty(beanProperty, propertyWrapper.finalSchema());
                    } else {
                        objectSchema.putOptionalProperty(beanProperty, propertyWrapper.finalSchema());
                    }

                    propertyEnhancer.fixupDateTimeSchema(beanProperty);
                    propertyEnhancer.processValidationConstraints(beanProperty);
                    if (propertyMetadata.hasDefaultValue() && propertyWrapper.finalSchema().asSimpleTypeSchema().getDefault() == null) {
                        propertyWrapper.finalSchema().asSimpleTypeSchema().setDefault(propertyMetadata.getDefaultValue());
                    }
                }
            }
            objectSchema.setId(WebMvcLinkBuilder.linkTo(controllerMethod).toUriComponentsBuilder().replaceQuery(null).build(false).toString());
            return objectSchema;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Fakes out just enough of a {@link BeanProperty} for things to work, for a method parameter.
     * The parameter can then be treated as if it were a property of an object for the purposes of schema generation.
     *
     * @param parameter the parameter in question
     * @param javaType the Jackson type of the parameter (from {@link SerializationConfig#constructType(Class)}).
     * @return a bean property instance
     */
    private BeanProperty beanPropertyForParameter(Parameter parameter, final JavaType javaType) {
        RequestParam ann = parameter.getAnnotation(RequestParam.class);
        String name = ann != null? StringUtils.defaultIfEmpty(ann.value(), ann.name()) : parameter.getName();
        JsonProperty jsonProperty = parameter.getAnnotation(JsonProperty.class);
        JsonPropertyDescription jsonPropertyDescription = parameter.getAnnotation(JsonPropertyDescription.class);
        @SuppressWarnings("deprecation") // First arg changes from boolean to Boolean
        PropertyMetadata propertyMetadata = PropertyMetadata.construct(
                (jsonProperty != null && jsonProperty.required()) || (ann != null && ann.required() && ann.defaultValue().equals(ValueConstants.DEFAULT_NONE)),
                jsonPropertyDescription != null ? jsonPropertyDescription.value() : null,
                jsonProperty != null && jsonProperty.index() != JsonProperty.INDEX_UNKNOWN ? jsonProperty.index() : null,
                jsonProperty != null && !jsonProperty.defaultValue().isEmpty() ? jsonProperty.defaultValue() :
                        (ann != null && !ann.defaultValue().equals(ValueConstants.DEFAULT_NONE) ? ann.defaultValue() : null));

        return (BeanProperty) Proxy.newProxyInstance(getClass().getClassLoader(), new Class[]{BeanProperty.class}, (proxy, method, args) -> switch (method.getName()) {
            case "getAnnotation" -> //noinspection unchecked
                    parameter.getAnnotation((Class<Annotation>) args[0]);
            case "getType" -> javaType;
            case "getName" -> name;
            case "getMetadata" -> propertyMetadata;
            case "getMember" -> null;
            case "findPropertyFormat" -> JsonFormat.Value.empty();
            default -> throw new UnsupportedOperationException(method.getName());
        });
    }

    private <T extends RepresentationModel<T>> JsonSchema createFullSchema(Class<T> resourceClass) {
        try {
            SchemaFactoryWrapper wrapper = new CustomSchemaFactoryWrapper(enumSchemaCreator, objectMapper, applicationContext);
            objectMapper.acceptJsonFormatVisitor(objectMapper.constructType(resourceClass), wrapper);
            return wrapper.finalSchema();
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        }
    }

    private void decorateJsonSchema(JsonSchema jsonSchema, Object schemaMethod, Optional<Object> listControllerMethod, Optional<Object> createControllerMethod) {
        jsonSchema.setId(WebMvcLinkBuilder.linkTo(schemaMethod).toString());
        decorateJsonSchema(jsonSchema, listControllerMethod, createControllerMethod);
    }

    private void decorateJsonSchema(JsonSchema jsonSchema, URI schemaUri, Optional<Object> listControllerMethod, Optional<Object> createControllerMethod) {
        jsonSchema.setId(schemaUri.toString());
        decorateJsonSchema(jsonSchema, listControllerMethod, createControllerMethod);
    }

    private void decorateJsonSchema(JsonSchema jsonSchema, Optional<Object> listControllerMethod, Optional<Object> createControllerMethod) {
        jsonSchema.set$schema(HTTP_JSON_SCHEMA_ORG_DRAFT_03_SCHEMA);

        Optional<LinkDescriptionObject> instances = listControllerMethod
                .map(m -> (m instanceof LinkDescriptionObject)? (LinkDescriptionObject) m : null);
        if (instances.isEmpty()) {
            instances = listControllerMethod
                    .flatMap(this::linkIfPermittedTo)
                    .map(l -> new LinkDescriptionObject().setHref(l.toString()).setRel("instances").setMethod(HttpMethod.GET.toString()));
        }

        Optional<LinkDescriptionObject> create = createControllerMethod
                        .map(m -> (m instanceof LinkDescriptionObject) ? (LinkDescriptionObject) m : null);
        if (create.isEmpty()) {
            create = createControllerMethod
                    .flatMap(this::linkIfPermittedTo)
                    .map(l -> new LinkDescriptionObject().setHref(l.toString()).setRel("create").setMethod(HttpMethod.POST.toString()));
        }

        LinkDescriptionObject[] linkDescriptionObjects = Stream.of(instances, create)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toArray(LinkDescriptionObject[]::new);

        if (linkDescriptionObjects.length > 0) {
            jsonSchema.asSimpleTypeSchema().setLinks(linkDescriptionObjects);
        }
    }

    Optional<LinkBuilderSupport<?>> linkIfPermittedTo(Object invocationValue) {
        LastInvocationAware invocations = DummyInvocationUtils.getLastInvocationAware(invocationValue);

        MethodInvocation invocation = invocations.getLastInvocation();
        if (securityChecker.check(invocation)) {
            return Optional.of(WebMvcLinkBuilder.linkTo(invocationValue));
        } else {
            return Optional.empty();
        }
    }

}
