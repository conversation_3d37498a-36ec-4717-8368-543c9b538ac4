package com.ecco.webApi.finance;

import static com.ecco.infrastructure.time.Formatters.formatDateMaybeTime;

import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

import com.ecco.dom.contacts.AddressHistory;
import com.ecco.dom.contracts.RateCard;
import com.ecco.finance.webApi.dto.ClientSalesChargeInvoiceDetailResource;
import com.ecco.finance.webApi.dto.FinanceChargeCalculation;
import com.ecco.finance.webApi.dto.FinanceChargeCalculation.ChargeChange;
import com.ecco.finance.webApi.dto.FinanceChargeCalculation.ChargeReason;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.google.common.collect.Range;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
@NullMarked
public class FinanceChargeCalculationDefault {

    private final FinanceChargeCalculation calc;

    // this is called with contract.getRateCards()
    public List<ClientSalesChargeInvoiceDetailResource.Line> calculateLines(Range<Instant> reportRange, List<AddressHistory> addressHistory, List<RateCard> rateCards) {
        assert reportRange.hasUpperBound(); // enforcing a report range makes logic easier

        var chargeChangeMoveInOut = generateAddressHistoryChanges(addressHistory).collect(Collectors.toList());

        // call calculateLines per getChargeNameId
        var rateCardsByChargeNameId = rateCards.stream()
                .collect(Collectors.groupingBy(RateCard::getChargeNameId));
        return rateCardsByChargeNameId.values().stream()
                .flatMap(rateCardGroup -> calc.calculateLines(reportRange, chargeChangeMoveInOut, rateCardGroup).stream())
                .collect(Collectors.toList());
    }

    private static Stream<ChargeChange> generateAddressHistoryChanges(List<AddressHistory> addressHistory) {
        return addressHistory.stream()
                .sorted(Comparator.comparing(AddressHistory::getValidFrom)) // comes reversed - see SRAddressController OrderByValidFromDesc
                .filter(a -> a.getBuildingLocationId() != null) // just count the move-ins because we get move in and out from one
                .mapMulti((a, out) -> {
                    assert a.getBuildingLocationId() != null;
                    String descIn = "Moved in [to b-id " + a.getBuildingLocationId() + "] on "
                            + formatDateMaybeTime(a.validFrom)
                            + (a.validTo != null ? " until " + formatDateMaybeTime(a.validTo) : "");
                    var moveIn = ChargeChange.buildCharge(
                            EccoTimeUtils.convertFromUsersLocalDateTime(a.validFrom).toInstant(), ChargeReason.MOVE_IN,
                            descIn, a.getServiceRecipientId(), a.getBuildingLocationId());
                    out.accept(moveIn);
                    if (a.validTo != null) {
                        String descOut = "Moved out [of b-id " + a.getBuildingLocationId() + "] on "
                                + formatDateMaybeTime(a.validTo);
                        var moveOut = ChargeChange.buildCharge(
                                EccoTimeUtils.convertFromUsersLocalDateTime(a.validTo).toInstant(),
                                ChargeReason.MOVE_OUT, descOut, a.getServiceRecipientId(), a.getBuildingLocationId());
                        out.accept(moveOut);
                    }
                });
    }

}
