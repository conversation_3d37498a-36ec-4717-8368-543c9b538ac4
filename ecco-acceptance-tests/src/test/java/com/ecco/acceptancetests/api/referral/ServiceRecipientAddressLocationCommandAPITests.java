package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.actors.CalendarActor;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ServiceRecipientCalendarEntryCommandViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.contacts.address.ServiceRecipientAddressLocationChangeCommandViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.rota.ServiceRecipientResourceScheduleCommandDto;
import com.ecco.webApi.viewModels.Result;
import com.ecco.calendar.core.webapi.EventResource;
import org.assertj.core.api.Assertions;
import org.jspecify.annotations.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientResponseException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static com.ecco.config.dom.ListDefinitionEntry.BUILDING_RESOURCETYPE_ID;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.junit.Assert.*;

public class ServiceRecipientAddressLocationCommandAPITests extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;
    static private ReferralViewModel rvm;
    static private ClientViewModel cvm;

    protected final ServiceOptions service = ServiceOptions.ACCOMMODATION;

    @BeforeEach
    public void createClientAndReferral() {
        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            String lastNameUnique = unique.clientLastNameFor("Turner");
            long clientId = clientActor.createClient("Billy", lastNameUnique);
            rvm = new ReferralViewModel();
            rvm.setClientId(clientId);
            rvm.setReceivedDate(org.joda.time.LocalDate.now());
            rvm.setImportServiceName(ServiceOptions.ACCOMMODATION.getServiceName());
            long rId = referralActor.createReferralFromCommand(rvm);
            rvm = referralActor.getReferralById(rId).getBody();
            cvm = clientActor.getClientById(rvm.clientId).getBody();
        }
    }

    @Test
    public void canChangeReferralContactLocation() {
        int idJim = createAddress("adrLoc1");


        // GIVEN a calendar entry
        String entryUuid;
        {
            CalendarEntryCommandViewModel cvm = new CalendarEntryCommandViewModel(BaseCommandViewModel.OPERATION_ADD, null);
            ServiceRecipientCalendarEntryCommandViewModel svm = new ServiceRecipientCalendarEntryCommandViewModel(rvm.serviceRecipientId, cvm);
            svm.calendarEntryViewModel.setStartDate(ChangeViewModel.changeNullTo(new org.joda.time.LocalDate()));
            svm.calendarEntryViewModel.setAllDay(ChangeViewModel.changeNullTo(true));
            Result result = commandActor.executeCommand(svm).getBody();
            entryUuid = CalendarActor.extractEventUuid(result.getLinks()[0].getHref());
            assertTrue(result.isCommandSuccessful());
            assertNotNull(result.getId());

            // ensure it has a location
            ResponseEntity<EventResource[]> events = calendarActor.getEntries(entryUuid);
            EventResource e = events.getBody()[0];
            assertEquals(entryUuid, e.getEntryId());
            assertNotNull("address is null", ServiceRecipientAddressLocationCommandAPITests.cvm.getAddress());
            assertEquals("non blank, space, required for JSONArray line 217, Lewisham, SE13", ServiceRecipientAddressLocationCommandAPITests.cvm.getAddress().toCommaSepString());
        }

        // WHEN change the client address
        {
            ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, rvm.serviceRecipientId, cvm.contactId.intValue());
            vm.addressLocation = changeNullTo(idJim);
            vm.validFrom = changeNullTo(LocalDateTime.now().minusMinutes(60));
            Result result = commandActor.executeCommand(vm).getBody();
            assertEquals(Result.COMMAND_APPLIED, result.getMessage());
            cvm = clientActor.getClientById(cvm.clientId).getBody();
            Assertions.assertThat(cvm.addressedLocationId).isEqualTo(idJim);
        }

        // THEN
        {
            // have the address updated on the calendar
            ResponseEntity<EventResource[]> events = calendarActor.getEntries(entryUuid);
            EventResource e = events.getBody()[0];
            assertEquals(entryUuid, e.getEntryId());
            // we are no longer changing address locations on events themselves
            //assertEquals("adrLoc1, Tawn, WA2 1BT", e.getLocation());
            assertEquals("non blank, space, required for JSONArray line 217, Lewisham, SE13", e.getLocation());
        }
    }

    @Test
    public void canChangeReferralContactLocationAndUpdate() {
        int idJim = createAddress("adrLoc1");

        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, rvm.serviceRecipientId, cvm.contactId.intValue());
        vm.addressLocation = changeNullTo(idJim);
        vm.validFrom = changeNullTo(LocalDateTime.now().minusMinutes(59));
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals(Result.COMMAND_APPLIED, result.getMessage());
        cvm = clientActor.getClientById(cvm.clientId).getBody();
        Assertions.assertThat(cvm.addressedLocationId).isEqualTo(idJim);
    }

    @Test
    public void canBookServiceRecipientToFixedContainer() {
        FixedContainerViewModel bvm = getFirstBuilding();

        // FIXME: We need to be stronger on intent on calendar - createEvent(withAttendeesTentative, attendeesConfirmed)
        //   respondToInvitation(entryUuid, response: accept/decline/maybe)
        ServiceRecipientResourceScheduleCommandDto dto = new ServiceRecipientResourceScheduleCommandDto(
                "add", rvm.serviceRecipientId); // <- srId to use to derive the calendarOwner (-> client or building or worker)
        dto.startDate = changeNullTo(LocalDate.now());
        dto.endDate = changeNullTo(LocalDate.now().plusDays(100));
        dto.assignedResourceSRId = changeNullTo(bvm.serviceRecipientId);
        dto.resourceTypeId = changeNullTo(BUILDING_RESOURCETYPE_ID);

        Result result = commandActor.executeCommand(dto).getBody();
        assertTrue(result.isCommandSuccessful());

        // Now check the resource schedule exists
// FIXME        assertThat("Expect link to appointment returned", appointmentHref, iterableWithSize(1));
    }

    @Test
    public void canChangeBuildingLocation() {
        int adrId = createAddress("adrBldg1");
        FixedContainerViewModel bvm = createBuildingWithAddress("bldg1", adrId, ChangeViewModel.changeNullTo(LocalDateTime.now()));

        bvm = buildingActor.getBuildingById(bvm.buildingId);
        Assertions.assertThat(bvm.locationId).isEqualTo(adrId);
    }

    @Test
    public void canGetBuilding() {
        buildingActor.getBuildingById(1252);
    }

    @Test
    public void canChangeClientToBuildingLocation() {
        int adrId = createAddress("adrBldg2");
        FixedContainerViewModel bvm = createBuildingWithAddress("bldg2", adrId, ChangeViewModel.changeNullTo(LocalDateTime.now()));

        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, rvm.serviceRecipientId, cvm.contactId.intValue());
        vm.validFrom = changeNullTo(LocalDateTime.now().minusMinutes(58));
        vm.buildingLocation = changeNullTo(bvm.buildingId);
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals(Result.COMMAND_APPLIED, result.getMessage());

        cvm = clientActor.getClientById(cvm.clientId).getBody();
        Assertions.assertThat(cvm.addressedLocationId).isEqualTo(adrId);
        Assertions.assertThat(cvm.residenceId).isEqualTo(bvm.buildingId);
    }

    @Test
    public void canChangeClientToBuildingLocationWithHistory() {
        // GIVEN bldg
        int adrId = createAddress("adrBldg3");
        LocalDateTime frm = LocalDateTime.now().minusMinutes(57);
        FixedContainerViewModel bvm = createBuildingWithAddress("bldg3", adrId, ChangeViewModel.changeNullTo(frm));

        // WHEN change client to bld
        createEntry(bvm, ChangeViewModel.create(null, frm));

        // THEN allocated and residingAt
        cvm = clientActor.getClientById(cvm.clientId).getBody();
        Assertions.assertThat(cvm.addressedLocationId).isEqualTo(adrId);
        Assertions.assertThat(cvm.residenceId).isEqualTo(bvm.buildingId);

        AddressHistoryViewModel[] vms = addressActor.getAddressHistoryByServiceRecipientIdOrderByValidFromDesc(rvm.serviceRecipientId).getBody();
        assertNotNull(vms);
        assertEquals(bvm.buildingId, vms[0].buildingId);
        // prevent rounding issues going into mysql
        assertTrue(vms[0].validFrom.toEpochSecond(ZoneOffset.UTC) - frm.toEpochSecond(ZoneOffset.UTC) < 2);
        assertNull(vms[0].validTo);
    }

    private void createEntry(FixedContainerViewModel bvm, ChangeViewModel<LocalDateTime> validFrom) {
        ServiceRecipientAddressLocationChangeCommandViewModel vm = createCmd(bvm.buildingId, validFrom);
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals(Result.COMMAND_APPLIED, result.getMessage());
    }

    @NonNull
    private ServiceRecipientAddressLocationChangeCommandViewModel createCmd(Integer buildingId, ChangeViewModel<LocalDateTime> validFrom) {
        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, rvm.serviceRecipientId, cvm.contactId.intValue());
        vm.buildingLocation = changeNullTo(buildingId);
        vm.validFrom = validFrom;
        return vm;
    }

    @Test
    public void cannotChangeToWhatIsNotThere() {
        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, rvm.serviceRecipientId, cvm.contactId.intValue());
        vm.validFrom = changeNullTo(LocalDateTime.now().minusMinutes(55));
        vm.addressLocation = changeNullTo(-999);
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void cannotUseUnknownServiceRecipient() {
        int idJim = createAddress("adrLoc2");
        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, -999, cvm.contactId.intValue());
        vm.validFrom = changeNullTo(LocalDateTime.now().minusMinutes(54));
        vm.addressLocation = changeNullTo(idJim);
        assertThrows(RestClientResponseException.class, () -> commandActor.executeCommand(vm));
    }

    private FixedContainerViewModel getFirstBuilding() {
        List<FixedContainerViewModel> buildings = buildingActor.findBuildings();

        return buildings.isEmpty() ? buildingActor.createBuilding(unique.nameFor("building"), null,
            null) : buildings.get(0);
    }

    private FixedContainerViewModel createBuildingWithAddress(String name, int adrId, ChangeViewModel<LocalDateTime> validFrom) {
        FixedContainerViewModel bvm = buildingActor.createBuilding(unique.nameFor(name), null, null);

        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, bvm.serviceRecipientId, null);
        vm.validFrom = validFrom;
        vm.addressLocation = changeNullTo(adrId);
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals(Result.COMMAND_APPLIED, result.getMessage());

        return bvm;
    }

    private int createAddress(String line1) {
        AddressViewModel avm = new AddressViewModel();
        String[] adr = {line1};
        avm.setAddress(adr);
        avm.setPostcode("WA2 1BT");
        avm.setTown("Tawn");
        String aId = addressActor.createAddress(avm).getBody().getId();
        return Integer.parseInt(aId);
    }

}
